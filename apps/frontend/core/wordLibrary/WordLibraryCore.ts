/**
 * 词库管理核心逻辑
 * 🎯 核心价值：统一的词库数据管理，支持29个颜色级别组合的词库系统
 * 📦 功能范围：词库创建、词语管理、验证检测、重复处理
 * 🔄 架构设计：基于Map的高性能数据结构，支持实时验证和同步
 */

import type {
  BasicColorType,
  DataLevel,
  WordEntry,
  WordLibrary,
  WordLibraryKey,
  WordLibraryState,
  WordValidationResult
} from '../matrix/MatrixTypes';

import { createWordLibraryKey } from '../matrix/MatrixTypes';

import { AVAILABLE_LEVELS, DEFAULT_COLOR_VALUES } from '../data/GroupAData';

// ===== 常量定义 =====

/** 词语长度限制 */
export const WORD_LENGTH_LIMITS = {
  MIN: 2,
  MAX: 4
} as const;

/** 支持的颜色顺序（按UI显示顺序） */
export const COLOR_DISPLAY_ORDER: BasicColorType[] = [
  'black', 'red', 'orange', 'yellow', 'green', 'cyan', 'blue', 'purple', 'pink'
];

/** 重复词语高亮颜色池 */
export const DUPLICATE_HIGHLIGHT_COLORS = [
  '#ffeb3b', '#ff9800', '#e91e63', '#9c27b0',
  '#673ab7', '#3f51b5', '#2196f3', '#00bcd4',
  '#009688', '#4caf50', '#8bc34a', '#cddc39',
  '#ffc107', '#ff5722', '#795548', '#607d8b'
] as const;

/** 所有可用的词库配置 */
export const AVAILABLE_WORD_LIBRARIES: Array<{ color: BasicColorType; level: DataLevel }> = [];

// 初始化可用词库列表
COLOR_DISPLAY_ORDER.forEach(color => {
  const availableLevels = AVAILABLE_LEVELS[color];
  availableLevels.forEach(level => {
    AVAILABLE_WORD_LIBRARIES.push({ color, level: level as DataLevel });
  });
});

// ===== 词库工具函数 =====

/**
 * 创建空词库
 */
export const createEmptyWordLibrary = (color: BasicColorType, level: DataLevel): WordLibrary => ({
  key: createWordLibraryKey(color, level),
  color,
  level,
  words: [],
  collapsed: false,
  lastUpdated: new Date()
});

/**
 * 创建词语条目
 */
export const createWordEntry = (text: string, color: BasicColorType, level: DataLevel): WordEntry => ({
  id: generateWordId(),
  text: text.trim(),
  color,
  level,
  createdAt: new Date(),
  updatedAt: new Date(),
  usageCount: 0,
  usagePositions: []
});

/**
 * 生成词语唯一ID
 */
export const generateWordId = (): string => {
  return `word_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
};

/**
 * 验证词语格式
 */
export const validateWordText = (text: string): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  const trimmedText = text.trim();

  // 检查长度
  if (trimmedText.length < WORD_LENGTH_LIMITS.MIN) {
    errors.push(`词语长度不能少于${WORD_LENGTH_LIMITS.MIN}个字符`);
  }
  if (trimmedText.length > WORD_LENGTH_LIMITS.MAX) {
    errors.push(`词语长度不能超过${WORD_LENGTH_LIMITS.MAX}个字符`);
  }

  // 检查是否为空
  if (!trimmedText) {
    errors.push('词语不能为空');
  }

  // 检查是否包含中文字符
  const chineseRegex = /[\u4e00-\u9fff]/;
  if (!chineseRegex.test(trimmedText)) {
    errors.push('词语必须包含中文字符');
  }

  // 检查是否包含特殊字符
  const specialCharsRegex = /[，。！？；：""''（）【】《》]/;
  if (specialCharsRegex.test(trimmedText)) {
    errors.push('词语不能包含标点符号');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * 检查词语重复
 */
export const checkWordDuplicate = (
  text: string,
  _currentLibraryKey: WordLibraryKey, // 保留参数以保持API兼容性
  libraries: Map<WordLibraryKey, WordLibrary>
): { isDuplicate: boolean; duplicateLibraries: WordLibraryKey[] } => {
  const trimmedText = text.trim();
  const duplicateLibraries: WordLibraryKey[] = [];

  libraries.forEach((library, key) => {
    const hasWord = library.words.some(word => word.text === trimmedText);
    if (hasWord) {
      duplicateLibraries.push(key);
    }
  });

  return {
    isDuplicate: duplicateLibraries.length > 0,
    duplicateLibraries
  };
};

/**
 * 输入验证（阻止性）- 用于UI层验证，阻止无效输入
 */
export const validateInputBeforeSubmit = (
  text: string,
  libraryKey: WordLibraryKey,
  globalIndex: Map<string, Set<WordLibraryKey>>
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  const trimmedText = text.trim();

  // 1. 格式验证
  const formatValidation = validateWordText(trimmedText);
  if (!formatValidation.isValid) {
    errors.push(...formatValidation.errors);
  }

  // 2. 同一词库内重复检查（阻止性）
  if (isSameLibraryDuplicate(globalIndex, trimmedText, libraryKey)) {
    errors.push(`词语"${trimmedText}"在当前词库中已存在`);
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * 跨词库重复检测（提醒性）- 用于数据录入后的高亮提醒
 */
export const detectCrossLibraryDuplicates = (
  text: string,
  globalIndex: Map<string, Set<WordLibraryKey>>
): { isDuplicate: boolean; duplicateLibraries: WordLibraryKey[] } => {
  const trimmedText = text.trim();
  const result = isCrossLibraryDuplicate(globalIndex, trimmedText);
  return {
    isDuplicate: result.isDuplicate,
    duplicateLibraries: result.libraries
  };
};

/**
 * 完整的词语验证（增强版）- 保持向后兼容
 * @deprecated 建议使用 validateInputBeforeSubmit 和 detectCrossLibraryDuplicates
 */
export const validateWord = (
  text: string,
  libraryKey: WordLibraryKey,
  libraries: Map<WordLibraryKey, WordLibrary>
): WordValidationResult => {
  const formatValidation = validateWordText(text);
  const duplicateCheck = checkWordDuplicate(text, libraryKey, libraries);

  // 检查是否在同一词库内重复
  const currentLibrary = libraries.get(libraryKey);
  const isInSameLibrary = currentLibrary?.words.some(word => word.text === text.trim()) || false;

  // 如果在同一词库内重复，则直接禁止
  if (isInSameLibrary) {
    return {
      isValid: false,
      errors: [...formatValidation.errors, `词语"${text}"在当前词库中已存在`],
      isDuplicate: true,
      duplicateLibraries: [libraryKey]
    };
  }

  // 如果格式无效，直接返回
  if (!formatValidation.isValid) {
    return {
      isValid: false,
      errors: formatValidation.errors,
      isDuplicate: duplicateCheck.isDuplicate,
      duplicateLibraries: duplicateCheck.duplicateLibraries
    };
  }

  return {
    isValid: true,
    errors: [],
    isDuplicate: duplicateCheck.isDuplicate,
    duplicateLibraries: duplicateCheck.duplicateLibraries
  };
};

/**
 * 解析输入文本为词语数组
 */
export const parseInputText = (input: string): string[] => {
  return input
    .split(/[，,]/) // 支持中英文逗号分割
    .map(word => word.trim())
    .filter(word => word.length > 0);
};

/**
 * 初始化词库状态
 */
export const createInitialWordLibraryState = (): WordLibraryState => {
  const libraries = new Map<WordLibraryKey, WordLibrary>();

  // 创建所有可用的词库
  AVAILABLE_WORD_LIBRARIES.forEach(({ color, level }) => {
    const library = createEmptyWordLibrary(color, level);

    // 为黑色1级词库添加默认测试数据
    if (color === 'black' && level === 1) {
      const defaultWord = createWordEntry('主题', color, level);
      library.words.push(defaultWord);
      library.lastUpdated = new Date();
    }

    libraries.set(library.key, library);
  });

  // 构建全局词语索引
  const globalWordIndex = buildGlobalWordIndex(libraries);

  return {
    libraries,
    activeLibrary: null,
    duplicateWords: new Map(),
    wordHighlightColors: new Map(),
    usedHighlightColors: new Set(),
    globalWordIndex,
    isLoading: false,
    lastSyncTime: null
  };
};

/**
 * 获取词库显示名称
 */
export const getWordLibraryDisplayName = (color: BasicColorType, level: DataLevel): string => {
  const colorNames: Record<BasicColorType, string> = {
    black: '黑色',
    red: '红色',
    orange: '橙色',
    yellow: '黄色',
    green: '绿色',
    cyan: '青色',
    blue: '蓝色',
    purple: '紫色',
    pink: '粉色'
  };

  return `${colorNames[color]}${level}级`;
};

/**
 * 获取词库背景色（与矩阵系统同步）
 */
export const getWordLibraryBackgroundColor = (color: BasicColorType): string => {
  return DEFAULT_COLOR_VALUES[color].hex;
};

/**
 * 获取文字颜色（基于背景色自动适配）
 */
export const getWordLibraryTextColor = (backgroundColor: string): string => {
  // 简单的亮度检测
  const isDark = backgroundColor === '#000000' ||
    (backgroundColor.startsWith('#') && parseInt(backgroundColor.slice(1), 16) < 0x808080);

  return isDark ? '#ffffff' : '#000000';
};

/**
 * 更新重复词语映射
 */
export const updateDuplicateWordsMap = (libraries: Map<WordLibraryKey, WordLibrary>): Map<string, WordLibraryKey[]> => {
  const duplicateMap = new Map<string, WordLibraryKey[]>();

  // 收集所有词语
  const wordMap = new Map<string, WordLibraryKey[]>();

  libraries.forEach((library, key) => {
    library.words.forEach(word => {
      if (!wordMap.has(word.text)) {
        wordMap.set(word.text, []);
      }
      wordMap.get(word.text)!.push(key);
    });
  });

  // 找出重复的词语
  wordMap.forEach((libraryKeys, wordText) => {
    if (libraryKeys.length > 1) {
      duplicateMap.set(wordText, libraryKeys);
    }
  });

  return duplicateMap;
};

/**
 * 更新重复词语映射和高亮颜色（增强版）
 */
export const updateDuplicateWordsMapWithColors = (
  libraries: Map<WordLibraryKey, WordLibrary>,
  currentWordHighlightColors: Map<string, string>,
  currentUsedColors: Set<string>
): {
  duplicateWords: Map<string, WordLibraryKey[]>;
  wordHighlightColors: Map<string, string>;
  usedHighlightColors: Set<string>;
} => {
  const duplicateMap = new Map<string, WordLibraryKey[]>();
  const newWordHighlightColors = new Map<string, string>();
  const newUsedColors = new Set<string>(currentUsedColors); // 保留已使用的颜色

  // 收集所有词语
  const wordMap = new Map<string, WordLibraryKey[]>();

  libraries.forEach((library, key) => {
    library.words.forEach(word => {
      if (!wordMap.has(word.text)) {
        wordMap.set(word.text, []);
      }
      wordMap.get(word.text)!.push(key);
    });
  });

  // 找出重复的词语并分配高亮颜色
  wordMap.forEach((libraryKeys, wordText) => {
    if (libraryKeys.length > 1) {
      // 这是重复词语
      duplicateMap.set(wordText, libraryKeys);

      // 为重复词语分配高亮颜色
      let highlightColor = currentWordHighlightColors.get(wordText);

      if (!highlightColor) {
        // 如果还没有颜色，分配一个新的
        highlightColor = generateRandomHighlightColor(newUsedColors);
        newUsedColors.add(highlightColor);
      }

      newWordHighlightColors.set(wordText, highlightColor);
    }
  });

  return {
    duplicateWords: duplicateMap,
    wordHighlightColors: newWordHighlightColors,
    usedHighlightColors: newUsedColors
  };
};

// ===== 全局词语索引管理 =====

/**
 * 构建全局词语索引
 */
export const buildGlobalWordIndex = (libraries: Map<WordLibraryKey, WordLibrary>): Map<string, Set<WordLibraryKey>> => {
  const index = new Map<string, Set<WordLibraryKey>>();

  libraries.forEach((library, libraryKey) => {
    library.words.forEach(word => {
      if (!index.has(word.text)) {
        index.set(word.text, new Set());
      }
      index.get(word.text)!.add(libraryKey);
    });
  });

  return index;
};

/**
 * 添加词语到全局索引
 */
export const addWordToGlobalIndex = (
  index: Map<string, Set<WordLibraryKey>>,
  word: string,
  libraryKey: WordLibraryKey
): void => {
  if (!index.has(word)) {
    index.set(word, new Set());
  }
  index.get(word)!.add(libraryKey);
};

/**
 * 从全局索引移除词语
 */
export const removeWordFromGlobalIndex = (
  index: Map<string, Set<WordLibraryKey>>,
  word: string,
  libraryKey: WordLibraryKey
): void => {
  const libraries = index.get(word);
  if (libraries) {
    libraries.delete(libraryKey);
    if (libraries.size === 0) {
      index.delete(word);
    }
  }
};

/**
 * 快速检查同一词库内是否重复
 */
export const isSameLibraryDuplicate = (
  index: Map<string, Set<WordLibraryKey>>,
  word: string,
  libraryKey: WordLibraryKey
): boolean => {
  const libraries = index.get(word);
  return libraries ? libraries.has(libraryKey) : false;
};

/**
 * 快速检查是否跨词库重复
 */
export const isCrossLibraryDuplicate = (
  index: Map<string, Set<WordLibraryKey>>,
  word: string
): { isDuplicate: boolean; libraries: WordLibraryKey[] } => {
  const libraries = index.get(word);
  if (!libraries || libraries.size <= 1) {
    return { isDuplicate: false, libraries: [] };
  }
  return { isDuplicate: true, libraries: Array.from(libraries) };
};

/**
 * 获取词语所在的所有词库
 */
export const getWordLibraries = (
  index: Map<string, Set<WordLibraryKey>>,
  word: string
): WordLibraryKey[] => {
  const libraries = index.get(word);
  return libraries ? Array.from(libraries) : [];
};

// ===== 随机颜色管理 =====

/**
 * 生成随机高亮颜色
 */
export const generateRandomHighlightColor = (usedColors: Set<string>): string => {
  const availableColors = DUPLICATE_HIGHLIGHT_COLORS.filter(color => !usedColors.has(color));

  if (availableColors.length === 0) {
    // 如果所有颜色都用完了，重新开始
    return DUPLICATE_HIGHLIGHT_COLORS[Math.floor(Math.random() * DUPLICATE_HIGHLIGHT_COLORS.length)];
  }

  return availableColors[Math.floor(Math.random() * availableColors.length)];
};

/**
 * 获取词语的高亮颜色
 */
export const getWordHighlightColor = (
  word: string,
  wordColorMap: Map<string, string>,
  usedColors: Set<string>
): string => {
  if (wordColorMap.has(word)) {
    return wordColorMap.get(word)!;
  }

  const newColor = generateRandomHighlightColor(usedColors);
  wordColorMap.set(word, newColor);
  usedColors.add(newColor);

  return newColor;
};
